#!/usr/bin/env python3
"""
Test script to verify the async fix for MCP tools in CrewAI
"""

import requests
import json
import time

def test_crewai_chat():
    """Test the CrewAI chat endpoint that was failing before"""
    
    # Test data - similar to what was failing before
    test_data = {
        "message": "What were my AWS costs for August 2025?",
        "session_id": "test_async_fix_session",
        "crew_type": "cost_analysis"
    }
    
    print("🧪 Testing CrewAI chat endpoint with MCP tools...")
    print(f"Request: {test_data}")
    
    try:
        # Make the request
        response = requests.post(
            "http://localhost:8000/chat",
            json=test_data,
            timeout=60
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ SUCCESS: Request completed without async errors!")
            print(f"Response: {json.dumps(result, indent=2)}")
            return True
        else:
            print(f"❌ FAILED: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ FAILED: Request timed out")
        return False
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

def test_api_health():
    """Test basic API health"""
    try:
        response = requests.get("http://localhost:8000/", timeout=10)
        if response.status_code == 200:
            print("✅ API is healthy")
            return True
        else:
            print(f"❌ API health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API health check failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing async fix for CrewAI MCP tools")
    print("=" * 50)
    
    # Test API health first
    if not test_api_health():
        print("API is not available, exiting...")
        exit(1)
    
    # Wait a moment for the API to be fully ready
    print("⏳ Waiting for API to be fully ready...")
    time.sleep(2)
    
    # Test the chat functionality
    success = test_crewai_chat()
    
    print("=" * 50)
    if success:
        print("🎉 All tests passed! The async fix is working correctly.")
    else:
        print("💥 Tests failed. The async issue may still exist.")
